/* 主题1: 沉稳商务 */
.theme-business {
  background: linear-gradient(135deg, #1e3a8a 0%, #312e81 100%);
  color: #f8fafc;
}

.theme-business .title {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.theme-business .description {
  color: #cbd5e1;
}

.theme-business .cta-button {
  background: #fbbf24;
  color: #1f2937;
}

.theme-business .cta-button:hover {
  background: #f59e0b;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
}

.theme-business .hero-image {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

/* 主题2: 赛伯朋克 */
.theme-cyberpunk {
  background: #0a0a0a;
  color: #ffffff;
}

.theme-cyberpunk .title {
  color: #00d9ff;
  font-family: 'Arial', sans-serif;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.theme-cyberpunk .description {
  color: #b0b0b0;
  font-family: 'Arial', sans-serif;
}

.theme-cyberpunk .cta-button {
  background: #ff0080;
  color: #ffffff;
  border: 2px solid #ff0080;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-family: 'Arial', sans-serif;
}

.theme-cyberpunk .cta-button:hover {
  background: #ffffff;
  color: #ff0080;
  border-color: #ffffff;
  transform: translateY(-1px);
}

.theme-cyberpunk .hero-image {
  border: 2px solid #00d9ff;
  filter: contrast(1.2) brightness(0.9);
}

/* 主题3: 年轻活力 */
.theme-vibrant {
  background: #f5f3ff;
  color: #1f2937;
}

.theme-vibrant .title {
  color: #7c3aed;
  font-weight: 800;
}

.theme-vibrant .description {
  color: #6b7280;
}

.theme-vibrant .cta-button {
  background: #ec4899;
  color: #ffffff;
  border: none;
  font-weight: 700;
}

.theme-vibrant .cta-button:hover {
  background: #db2777;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(236, 72, 153, 0.3);
}

.theme-vibrant .hero-image {
  border: 3px solid #ec4899;
  filter: saturate(1.1);
}

/* 主题4: 极简主义 */
.theme-minimal {
  background: #ffffff;
  color: #1f2937;
}

.theme-minimal .title {
  color: #111827;
  font-weight: 300;
  letter-spacing: -0.02em;
}

.theme-minimal .description {
  color: #6b7280;
  font-weight: 400;
}

.theme-minimal .cta-button {
  background: #111827;
  color: #ffffff;
  border: 1px solid #111827;
  font-weight: 400;
}

.theme-minimal .cta-button:hover {
  background: #ffffff;
  color: #111827;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(17, 24, 39, 0.15);
}

.theme-minimal .hero-image {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  filter: grayscale(0.1);
}

/* 主题5: 暖色温馨 */
.theme-warm {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
}

.theme-warm .title {
  color: #d2691e;
  text-shadow: 0 2px 4px rgba(139, 69, 19, 0.1);
}

.theme-warm .description {
  color: #a0522d;
}

.theme-warm .cta-button {
  background: #ff8c00;
  color: #ffffff;
  border: none;
}

.theme-warm .cta-button:hover {
  background: #ff7700;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 140, 0, 0.3);
}

.theme-warm .hero-image {
  box-shadow: 0 15px 30px rgba(255, 140, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.5);
  filter: sepia(0.2) saturate(1.1) brightness(1.05);
}
