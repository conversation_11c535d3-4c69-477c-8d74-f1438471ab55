<template>
  <div class="image-switcher">
    <!-- 桌面端样式 -->
    <div class="desktop-switcher">
      <div class="switcher-header">
        <h3>图片选择</h3>
      </div>
      <div class="image-toggle">
        <button
          v-for="image in images"
          :key="image.id"
          @click="switchImage(image.id)"
          :class="{ active: currentImage === image.id }"
          class="image-btn"
          :title="image.name"
        >
          <img :src="image.src" :alt="image.name" class="image-preview" />
          <span class="image-name">{{ image.name }}</span>
        </button>
      </div>
    </div>

    <!-- 移动端样式 -->
    <div class="mobile-switcher">
      <div class="mobile-dropdown" @click="toggleDropdown">
        <img :src="getCurrentImage().src" :alt="getCurrentImage().name" class="current-image-preview" />
        <span class="current-image-name">{{ getCurrentImage().name }}</span>
        <svg class="dropdown-icon" :class="{ rotated: isDropdownOpen }" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="6,9 12,15 18,9"></polyline>
        </svg>
      </div>
      <div class="mobile-options" :class="{ show: isDropdownOpen }">
        <button
          v-for="image in images"
          :key="image.id"
          @click="selectImage(image.id)"
          :class="{ active: currentImage === image.id }"
          class="mobile-option"
        >
          <img :src="image.src" :alt="image.name" class="option-image-preview" />
          <span>{{ image.name }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Image {
  id: string
  name: string
  src: string
}

interface Props {
  images: Image[]
  currentImage: string
}

interface Emits {
  (e: 'switch-image', imageId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isDropdownOpen = ref(false)

const switchImage = (imageId: string) => {
  emit('switch-image', imageId)
}

const getCurrentImage = () => {
  return props.images.find(img => img.id === props.currentImage) || props.images[0]
}

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
}

const selectImage = (imageId: string) => {
  switchImage(imageId)
  isDropdownOpen.value = false
}
</script>

<style scoped>
.image-switcher {
  position: relative;
}

/* 桌面端样式 */
.desktop-switcher {
  display: block;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-width: 140px;
}

.switcher-header {
  text-align: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.switcher-header h3 {
  margin: 0;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.image-toggle {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
  border: none;
  border-radius: 8px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  flex-direction: column;
  gap: 4px;
}

.image-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.image-btn.active {
  background: #3b82f6;
  color: white;
}

.image-name {
  font-size: 11px;
  font-weight: 500;
}

.image-preview {
  width: 50px;
  height: 30px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.image-btn.active .image-preview {
  border-color: #3b82f6;
}

/* 移动端样式 */
.mobile-switcher {
  display: none;
  position: relative;
}

.mobile-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  min-width: 120px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.current-image-preview {
  width: 24px;
  height: 18px;
  object-fit: cover;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.current-image-name {
  flex: 1;
  text-align: left;
  font-size: 11px;
}

.dropdown-icon {
  width: 14px;
  height: 14px;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

.mobile-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  margin-top: 4px;
}

.mobile-options.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.mobile-option {
  width: 100%;
  padding: 8px 10px;
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-option:hover {
  background: rgba(0, 0, 0, 0.05);
}

.mobile-option.active {
  background: #3b82f6;
  color: white;
}

.mobile-option:first-child {
  border-radius: 8px 8px 0 0;
}

.mobile-option:last-child {
  border-radius: 0 0 8px 8px;
}

.option-image-preview {
  width: 24px;
  height: 18px;
  object-fit: cover;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.mobile-option.active .option-image-preview {
  border-color: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .desktop-switcher {
    padding: 8px;
    min-width: 120px;
  }

  .image-btn {
    padding: 6px 8px;
  }

  .image-name {
    font-size: 10px;
  }

  .image-preview {
    width: 40px;
    height: 24px;
  }
}

@media (max-width: 480px) {
  .desktop-switcher {
    display: none;
  }

  .mobile-switcher {
    display: block;
  }
}
</style>
