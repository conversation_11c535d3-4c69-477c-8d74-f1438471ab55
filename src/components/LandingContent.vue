<template>
  <div class="container">
    <!-- 文案区 -->
    <div class="content-section">
      <h1 class="title">🌱 Bergabunglah dengan Komunitas Belajar Kreator</h1>
      <p class="main-description">
        Selamat datang di komunitas digital yang berfokus pada kreativitas dan pembelajaran.
      </p>

      <!-- 功能列表 -->
      <div class="features-list">
        <div class="feature-item">
          <span class="feature-icon">✅</span>
          <span class="feature-text">Bebas berbagi ide-ide kreatif</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">✅</span>
          <span class="feature-text">Belajar dari pengalaman dan tips orang lain</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">✅</span>
          <span class="feature-text">Mengikuti tren terbaru dalam dunia konten digital</span>
        </div>
      </div>

      <a
        href="https://t.me/+_XOqu3UK9i9jZTNl"
        target="_blank"
        rel="noopener noreferrer"
        class="cta-button"
        >Gabung Sekarang</a
      >

    </div>

    <!-- 图片区 -->
    <div class="image-section">
      <img :src="currentImage.src" :alt="currentImage.name" class="hero-image" />

      <!-- 分隔线 -->
      <div class="divider">⸻</div>

      <!-- 免责声明 -->
      <div class="disclaimer-section">
        <h3 class="disclaimer-title">📢 Disclaimer</h3>
        <p class="disclaimer-text">
          Komunitas ini adalah platform terbuka untuk berbagi dan bertukar pengetahuan. Kami tidak terlibat dalam transaksi komersial atau bentuk ajakan apa pun.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Image {
  id: string
  name: string
  src: string
}

interface Props {
  currentImage: Image
}

defineProps<Props>()
</script>

<style scoped>
.container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.content-section {
  max-width: 500px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.main-description {
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.features-list {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.feature-icon {
  font-size: 1rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.feature-text {
  font-size: 1rem;
  line-height: 1.5;
  transition: all 0.3s ease;
}

.divider {
  text-align: center;
  font-size: 1.5rem;
  margin: 1rem 0 0.5rem 0;
  opacity: 0.6;
  transition: all 0.3s ease;
  width: 100%;
}

.disclaimer-section {
  width: 100%;
  max-width: 500px;
  padding: 1.25rem;
  border-radius: 0.75rem;
  background: rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  text-align: center;
}

.disclaimer-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  transition: all 0.3s ease;
}

.disclaimer-text {
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.cta-button {
  padding: 1rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.image-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.hero-image {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: 1rem;
  transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .title {
    font-size: 2rem;
  }

  .main-description {
    font-size: 1rem;
  }

  .feature-text {
    font-size: 0.95rem;
  }

  .disclaimer-section {
    padding: 1rem;
  }

  .disclaimer-title {
    font-size: 0.95rem;
  }

  .disclaimer-text {
    font-size: 0.8rem;
  }

  .cta-button {
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
    gap: 1.5rem;
  }

  .title {
    font-size: 1.75rem;
  }

  .main-description {
    font-size: 0.95rem;
  }

  .features-list {
    margin-bottom: 1.5rem;
  }

  .feature-item {
    margin-bottom: 0.5rem;
  }

  .feature-text {
    font-size: 0.9rem;
  }

  .divider {
    margin: 0.75rem 0 0.5rem 0;
    font-size: 1.25rem;
  }

  .disclaimer-section {
    padding: 0.875rem;
  }

  .disclaimer-title {
    font-size: 0.9rem;
  }

  .disclaimer-text {
    font-size: 0.75rem;
  }

  .cta-button {
    width: 100%;
    font-size: 1rem;
    padding: 0.875rem 1.5rem;
  }

  .hero-image {
    max-width: 100%;
    border-radius: 0.75rem;
  }
}

@media (max-width: 360px) {
  .title {
    font-size: 1.5rem;
  }

  .main-description {
    font-size: 0.9rem;
  }

  .feature-text {
    font-size: 0.85rem;
  }

  .disclaimer-title {
    font-size: 0.85rem;
  }

  .disclaimer-text {
    font-size: 0.7rem;
  }
}
</style>
