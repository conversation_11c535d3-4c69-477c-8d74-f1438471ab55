<template>
  <div class="container">
    <!-- 文案区 -->
    <div class="content-section">
      <h1 class="title">Tambah Duit!</h1>
      <p class="description">
        <PERSON>ab<PERSON> ke Grup Kami, Upload Video di TikTok Selesaikan tugasnya, bisa dapat duit!
      </p>
      <a
        href="https://t.me/+_XOqu3UK9i9jZTNl"
        target="_blank"
        rel="noopener noreferrer"
        class="cta-button"
        >Gabung Sekarang</a
      >
    </div>

    <!-- 图片区 -->
    <div class="image-section">
      <img :src="currentImage.src" :alt="currentImage.name" class="hero-image" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Image {
  id: string
  name: string
  src: string
}

interface Props {
  currentImage: Image
}

defineProps<Props>()
</script>

<style scoped>
.container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.content-section {
  max-width: 500px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.description {
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.cta-button {
  padding: 1rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.image-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: 1rem;
  transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .title {
    font-size: 2rem;
  }

  .description {
    font-size: 1rem;
  }

  .cta-button {
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
    gap: 1.5rem;
  }

  .title {
    font-size: 1.75rem;
  }

  .description {
    font-size: 0.95rem;
  }

  .cta-button {
    width: 100%;
    font-size: 1rem;
    padding: 0.875rem 1.5rem;
  }

  .hero-image {
    max-width: 100%;
    border-radius: 0.75rem;
  }
}

@media (max-width: 360px) {
  .title {
    font-size: 1.5rem;
  }

  .description {
    font-size: 0.9rem;
  }
}
</style>
