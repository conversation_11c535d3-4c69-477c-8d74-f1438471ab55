<template>
  <div class="theme-switcher">
    <!-- 桌面端样式 -->
    <div class="desktop-switcher">
      <div class="switcher-header">
        <h3>样式选择</h3>
      </div>
      <div class="theme-toggle">
        <button
          v-for="theme in themes"
          :key="theme.id"
          @click="switchTheme(theme.id)"
          :class="{ active: currentTheme === theme.id }"
          class="theme-btn"
          :title="theme.name"
        >
          <span class="theme-name">{{ theme.name }}</span>
        </button>
      </div>
    </div>

    <!-- 移动端样式 -->
    <div class="mobile-switcher">
      <div class="mobile-dropdown" @click="toggleDropdown">
        <span class="current-theme">{{ getCurrentThemeName() }}</span>
        <svg class="dropdown-icon" :class="{ rotated: isDropdownOpen }" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="6,9 12,15 18,9"></polyline>
        </svg>
      </div>
      <div class="mobile-options" :class="{ show: isDropdownOpen }">
        <button
          v-for="theme in themes"
          :key="theme.id"
          @click="selectTheme(theme.id)"
          :class="{ active: currentTheme === theme.id }"
          class="mobile-option"
        >
          {{ theme.name }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Theme {
  id: string
  name: string
}

interface Props {
  themes: Theme[]
  currentTheme: string
}

interface Emits {
  (e: 'switch-theme', themeId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isDropdownOpen = ref(false)

const switchTheme = (themeId: string) => {
  emit('switch-theme', themeId)
}

const getCurrentThemeName = () => {
  return props.themes.find(theme => theme.id === props.currentTheme)?.name || '样式'
}

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
}

const selectTheme = (themeId: string) => {
  switchTheme(themeId)
  isDropdownOpen.value = false
}
</script>

<style scoped>
.theme-switcher {
  position: relative;
}

/* 桌面端样式 */
.desktop-switcher {
  display: block;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-width: 140px;
}

.switcher-header {
  text-align: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.switcher-header h3 {
  margin: 0;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.theme-toggle {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.theme-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.theme-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.theme-btn.active {
  background: #3b82f6;
  color: white;
}

.theme-name {
  font-size: 11px;
  font-weight: 500;
}

/* 移动端样式 */
.mobile-switcher {
  display: none;
  position: relative;
}

.mobile-dropdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  min-width: 120px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.current-theme {
  flex: 1;
  text-align: left;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

.mobile-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  margin-top: 4px;
}

.mobile-options.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.mobile-option {
  width: 100%;
  padding: 10px 12px;
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.mobile-option:hover {
  background: rgba(0, 0, 0, 0.05);
}

.mobile-option.active {
  background: #3b82f6;
  color: white;
}

.mobile-option:first-child {
  border-radius: 8px 8px 0 0;
}

.mobile-option:last-child {
  border-radius: 0 0 8px 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .desktop-switcher {
    padding: 8px;
    min-width: 120px;
  }

  .theme-btn {
    padding: 6px 8px;
  }

  .theme-name {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .desktop-switcher {
    display: none;
  }

  .mobile-switcher {
    display: block;
  }
}
</style>
