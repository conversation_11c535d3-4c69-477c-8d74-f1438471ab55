<script setup lang="ts">
import { ref } from 'vue'
import ThemeSwitcher from '../components/ThemeSwitcher.vue'
import ImageSwitcher from '../components/ImageSwitcher.vue'
import LandingContent from '../components/LandingContent.vue'

import '../styles/themes.css'

interface Theme {
  id: string
  name: string
}

interface Image {
  id: string
  name: string
  src: string
}

const currentTheme = ref('cyberpunk')
const currentImage = ref('pic1')

const themes: Theme[] = [
  { id: 'business', name: '沉稳商务' },
  { id: 'cyberpunk', name: '赛伯朋克' },
  { id: 'vibrant', name: '年轻活力' },
  { id: 'minimal', name: '极简主义' },
  { id: 'warm', name: '暖色温馨' }
]

const images: Image[] = [
  { id: 'pic1', name: '图片1', src: '/imgs/pic1.jpg' },
  { id: 'Pic2', name: '图片2', src: '/imgs/Pic2.jpg' },
  { id: 'Pic3', name: '图片3', src: '/imgs/Pic3.jpg' },
  { id: 'Pic4', name: '图片4', src: '/imgs/Pic4.jpg' },
  { id: 'Pic5', name: '图片5', src: '/imgs/Pic5.jpg' }
]

const switchTheme = (themeId: string) => {
  currentTheme.value = themeId
}

const switchImage = (imageId: string) => {
  currentImage.value = imageId
}

const getCurrentImage = () => {
  return images.find(img => img.id === currentImage.value) || images[0]
}
</script>

<template>
  <main class="landing-page" :class="`theme-${currentTheme}`">
    <div class="combined-switcher">
      <ImageSwitcher
        :images="images"
        :currentImage="currentImage"
        @switch-image="switchImage"
      />
      <ThemeSwitcher
        :themes="themes"
        :currentTheme="currentTheme"
        @switch-theme="switchTheme"
      />
    </div>

    <LandingContent :currentImage="getCurrentImage()" />
  </main>
</template>

<style scoped>
.landing-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 2rem 0;
  transition: all 0.5s ease;
}

.combined-switcher {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: none;
  flex-direction: column;
  gap: 15px;
  align-items: flex-start;
  pointer-events: none;
}

.combined-switcher > * {
  pointer-events: auto;
}

@media (max-width: 768px) {
  .combined-switcher {
    top: 10px;
    right: 10px;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .combined-switcher {
    position: fixed;
    top: 10px;
    left: 10px;
    right: 10px;
    flex-direction: row;
    gap: 10px;
    align-items: center;
    justify-content: center;
  }

  .landing-page {
    padding: 1rem 0;
  }
}

@media (max-width: 360px) {
  .combined-switcher {
    top: 5px;
    left: 5px;
    right: 5px;
    gap: 8px;
  }

  .landing-page {
    padding: 1rem 0;
  }
}
</style>
